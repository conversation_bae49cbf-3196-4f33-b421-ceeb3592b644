server.port=7003
spring.application.name=service-rpc-eth

#Eureka
eureka.client.serviceUrl.defaultZone=http://************:7000/eureka/
eureka.instance.prefer-ip-address=true

#MongoDB
spring.data.mongodb.uri=mongodb://************:27017/wallet

#Kafka
spring.kafka.bootstrap-servers=************:9092
spring.kafka.consumer.group-id=default-group
spring.kafka.template.default-topic=test
spring.kafka.listener.concurrency=1
spring.kafka.producer.batch-size=1000

#\u5E01\u79CD\u4FE1\u606F\u914D\u7F6E
coin.rpc=http://127.0.0.1:8545
coin.name=Ethereum
coin.unit=ETH
coin.keystore-path=/www/wallet-data/ETH/keystore

# \u521D\u59CB\u5316\u5E01\u79CD\u4FE1\u606F
coin.init-block-height=11398709
coin.step=5
coin.withdraw-wallet=UTC--2020-12-06T01-48-09.229820289Z--f6dfdbd4a9260dce2909b1b0904524e000a1d5b8
coin.withdraw-wallet-password=bizzan
coin.gas-limit=200000
coin.min-collect-amount=0.001
coin.ignore-from-address=******************************************
watcher.init-block-height=11398709
watcher.step=5
watcher.confirmation=5
watcher.interval=5000
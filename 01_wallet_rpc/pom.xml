<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bizzan.wallet</groupId>
    <artifactId>wallet-rpc</artifactId>
    <packaging>pom</packaging>
    <version>1.2</version>
    <modules>
        <module>rpc-common</module>
        <module>bitcoin-rpc</module>
        <module>bitcoin</module>
        <module>usdt</module>
        <module>eth</module>
        <module>erc-token</module>
        <module>eth-support</module>
        <module>bch</module>
        <module>ltc</module>
        <module>erc-eusdt</module>
        <module>bsv</module>
        <module>eos</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.22.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project-version>1.2</project-version>
        <commons-lang3.version>3.4</commons-lang3.version>
        <spring-cloud.version>Edgware.RELEASE</spring-cloud.version>
        <jackson.version>2.12.1</jackson.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>3.1</version>
            </dependency>

            <dependency>
                <groupId>cash.bitcoinj</groupId>
                <artifactId>bitcoinj-core</artifactId>
                <version>0.14.5.2</version>
            </dependency>

            <!--必须排除jackson 低于2.9.9有远程执行高危漏洞-->
            <dependency>
                <groupId>com.github.briandilley.jsonrpc4j</groupId>
                <artifactId>jsonrpc4j</artifactId>
                <version>1.5.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--jackson 低于2.9.9有远程执行漏洞【高危】-->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.reactivex</groupId>
                <artifactId>rxjava</artifactId>
                <version>1.3.8</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <!--务必用高版本，1.2.48以下有序列化远程执行漏洞【高危】-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.75</version>
            </dependency>
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>core</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>1.5.9.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <optional>true</optional>
                <version>1.18.18</version>
            </dependency>
            <dependency>
                <groupId>com.spark.bc</groupId>
                <artifactId>bitcoin-rpc</artifactId>
                <version>1.2.0</version>
                <scope>system</scope>
				<systemPath>${project.basedir}/lib/bitcoin-rpc-1.2.0.jar</systemPath>
            </dependency>
            <dependency>
                <groupId>com.bizzan.wallet</groupId>
                <artifactId>rpc-common</artifactId>
                <version>${project-version}</version>
            </dependency>
            <dependency>
                <groupId>com.bizzan.wallet</groupId>
                <artifactId>bitcoin-rpc</artifactId>
                <version>${project-version}</version>
            </dependency>
            <dependency>
                <groupId>com.bizzan.wallet</groupId>
                <artifactId>eth-support</artifactId>
                <version>${project-version}</version>
            </dependency>
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>core</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.mashape.unirest</groupId>
                <artifactId>unirest-java</artifactId>
                <version>1.4.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
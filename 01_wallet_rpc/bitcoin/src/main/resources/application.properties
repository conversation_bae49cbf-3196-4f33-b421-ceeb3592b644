server.port=7001
spring.application.name=service-rpc-btc

#Eureka
eureka.client.serviceUrl.defaultZone=http://************:7000/eureka/
eureka.instance.prefer-ip-address=true

#MongoDB
spring.data.mongodb.uri=mongodb://************:27017/wallet

#Kafka
spring.kafka.bootstrap-servers=************:9092
spring.kafka.consumer.group-id=default-group
spring.kafka.template.default-topic=test
spring.kafka.listener.concurrency= 3
spring.kafka.producer.batch-size= 1000

coin.rpc=**************************************************************/
coin.name=Bitcoin
coin.unit=BTC
server.port=7009
spring.application.name=service-rpc-eos

#Eureka
eureka.client.serviceUrl.defaultZone=http://************:7000/eureka/
eureka.instance.prefer-ip-address=true

#MongoDB
spring.data.mongodb.uri=mongodb://************:27017/wallet

#Kafka
spring.kafka.bootstrap-servers=************:9092
spring.kafka.consumer.group-id=default-group
spring.kafka.template.default-topic=test
# \u5728\u4FA6\u542C\u5668\u5BB9\u5668\u4E2D\u8FD0\u884C\u7684\u7EBF\u7A0B\u6570
spring.kafka.listener.concurrency= 3
# \u8FD9\u6709\u52A9\u4E8E\u63D0\u5347\u5BA2\u6237\u7AEF\u548C\u670D\u52A1\u5668\u4E0A\u7684\u6027\u80FD\uFF0C\u6B64\u914D\u7F6E\u63A7\u5236\u9ED8\u8BA4\u6279\u91CF\u5927\u5C0F\uFF08\u4EE5\u5B57\u8282\u4E3A\u5355\u4F4D\uFF09\uFF0C\u9ED8\u8BA4\u503C\u4E3A16384
spring.kafka.producer.batch-size= 1000

bizzan.blockApi=https://open-api.eos.blockdog.com/
bizzan.apikey=a9cbd245-e976-458a-9c62-3d8d8c9df872

coin.rpc=
coin.name=EOS
coin.unit=EOS
coin.depositAddress=z5z5z5z5z5z5

watcher.init-block-height=79953165
watcher.step=100
watcher.confirmation=200
watcher.interval=20000
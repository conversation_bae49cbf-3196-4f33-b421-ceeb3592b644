server.port=7002
spring.application.name=service-rpc-usdt

#Eureka
eureka.client.serviceUrl.defaultZone=http://************:7000/eureka/
eureka.instance.prefer-ip-address=true

#MongoDB
spring.data.mongodb.uri=mongodb://************:27017/wallet

#Kafka
spring.kafka.bootstrap-servers=************:9092
spring.kafka.consumer.group-id=default-group
spring.kafka.template.default-topic=test
spring.kafka.listener.concurrency= 3
spring.kafka.producer.batch-size= 1000

coin.rpc=**************************************************************/
coin.name=USDT
coin.unit=USDT
coin.password=
coin.step=10
coin.init-block-height=631196
coin.withdraw-address=**********************************
coin.default-miner-fee=0.0001
coin.recharge-miner-fee=0.0001
coin.min-collect-amount=1
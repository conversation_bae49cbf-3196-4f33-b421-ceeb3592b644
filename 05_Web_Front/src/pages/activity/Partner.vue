<style lang="scss" scoped>
.about_us_container {
  // background-color: #eee;
  padding: 60px 0 20px 0;
  .wrapper {
    margin: 30px 12%;
    background-color: #192330;
    padding: 20px 40px;
    h2 {
      font-weight: 400;
      height: 50px;
      font-size: 20px;
      border-bottom: 1px solid #27313e;
      line-height: 50px;
    }
    .wrapper_logo ul {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 30px;
      border-bottom: 1px solid #27313e;
      height:280px;
      li {
        width: 25%;
        padding: 0 20px;
        list-style-type: none;
        border-right: 1px solid #27313e;
        text-align: center;
        min-height: 240px;
        img{
          width: 100px;
        }
        h5 {
          line-height: 50px;
          font-size: 20px;
        }
        span {
          color: #828ea1;
          font-size: 12px;
        }
      }
    }
    .content_wrapper {
      padding: 20px 0;
      .content1 {
        border-bottom: 1px solid #27313e;
        padding-bottom: 20px;
        margin-bottom: 20px;
        h5 {
          font-size: 18px;
          line-height: 40px;
          font-weight: 500;
          margin-bottom: 15px;
        }
        p {
          font-size: 14px;
          line-height: 30px;
          color: #828ea1;
          margin-bottom: 15px;
          text-indent: 30px;
          // margin-bottom: 14px;
          text-align: justify;
        }
      }
    }
  }
}
</style>

<template>
  <div class="about_us_container">
    <div class="wrapper">
      <h2 class="title">合伙人权益</h2>
      <div class="wrapper_logo">
        <ul>
          <li>
            <img src="../../assets/images/feature_safe.png" alt="">
            <h5>平台分红</h5>
            <span>除却运营成本，Const将80%的净利润分配给平台合伙人，因市场的无法预测，Const无法承诺绝对盈利，作为合伙人需与Const共同承担风险与收益。</span>
          </li>
          <li>
            <img src="../../assets/images/feature_fast.png" alt="">
            <h5>空投奖励</h5>
            <span>项目方如提供一些营销币、活动币，Const将与项目方协商，分出一定的比例由合伙人优先享有空投奖励。</span>
          </li>
          <li>
            <img src="../../assets/images/feature_global.png" alt="">
            <h5>{{$t('description.title3')}}</h5>
            <span>{{$t('description.message3')}}</span>
          </li>
          <li style="border:none;">
            <img src="../../assets/images/feature_choose.png" alt="">
            <h5>{{$t('description.title4')}}</h5>
            <span>{{$t('description.message4')}}</span>
          </li>
        </ul>
      </div>
      <div class="content_wrapper">
        <div class="content1">
          <h5>{{$t('cms.aboutus')}}</h5>
          <p>{{$t('description.aboutdesc1')}}</p>
          <p>{{$t('description.aboutdesc2')}} </p>
          <p>{{$t('description.aboutdesc3')}}</p>
          <p>{{$t('description.aboutdesc4')}}</p>
          <p>{{$t('description.aboutdesc5')}}</p>
        </div>
        <div class="content1" style="border:none;">
          <h5>{{$t('cms.contactus')}}</h5>
          <p>商务合作：</p>
          <p>客户服务：</p>
          <p>上币申请：</p>
          <p>媒体合作：</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  created() {
    this.init();
  },
  computed: {
    lang() {
      return this.$store.state.lang;
    }
  },
  methods: {
    init() {
      this.$store.commit("navigate", "nav-partner");
    }
  }
};
</script>
<style scoped>
.container {
  background: #fff;
  overflow-x: hidden;
}

.content-wrap {
  padding-top: 20px;
  position: relative;
  width: 1200px;
  margin: 0 auto;
  background: #fff;
  min-height: 600px;
}

.leftmenu {
  border: 1px solid #efefef;
}

.leftmenu .divider {
  font-size: 20px;
  padding: 5px;
  background: #efefef;
}

.leftmenu li {
  font-size: 14px;
  line-height: 30px;
  padding: 5px;
}

.leftmenu li a {
  color: #444;
}

.leftmenu li a:hover {
  color: #f0a70a;
}

.leftmenu li.cur a {
  color: #f0a70a;
}
.content {
  padding-top: 20px;
  /*padding-left: 200px;*/
  padding-bottom: 20px;
  text-align: left;
  padding-left: 20px;
}

.content .ivu-col {
  text-align: left;
}

.content p {
  text-indent: 2em;
  margin-bottom: 10px;
  font-size: 16px;
}
.content > div {
  padding-bottom: 20px;
}
.content h3 {
  font-size: 22px;
  font-weight: normal;
  margin-bottom: 20px;
}
.content h5 {
  font-size: 14px;
}
.WordSection1 span {
  color: black;
}
</style>

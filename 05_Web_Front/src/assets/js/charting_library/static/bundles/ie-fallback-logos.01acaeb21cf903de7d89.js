webpackJsonp([3], {
    535: function(A, e) {
        "use strict";
        Object.defineProperty(e, "__esModule", { value: !0 }), e.fallbackImages = { tvLogoBlack: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAAmCAMAAACmhKjHAAAAaVBMVEUAAAAAAAAAAAAFEhgrg6g4rNsznskLIy0AAAAQMkAAAAAUQFEAAAAAAAAdWXIAAAAkcI8ujbQgZYEZTWMAAAAAAAAAAAA2pNIxlb4AAAAAAAAxlb4oe50dWXEAAAAAAAAAAAAoeZs7s+RC6be/AAAAInRSTlMAmU2f2fnspiWsCbN8QsATzN/GuY+GN/LmX1bl0r9yaRzSe/OO0wAAAbFJREFUeAHFlevOoyAQhjsFFLX6eT5o+7XL/V/kwtAUGHqwySb7/CvxiS/DKz38c36klN9L5S9Yfsf8C20Bn3WvOvYQMuyLvIIhvfBta+oLIOMOb0GNqzuCM9CUP5+8I2hq5SE6m/e9K0HDVUgFluMb8RR41Dy9nO8IAJ2KEVubovkqrjn2ST3nj5lSnz/vCwBU6hVTBgBLrMkTGBqlaYqiiU2h4w5UyxewoAea2OQma0k8+zpW4GgK0BSxh4TzQe+SuEhEdB5caT+Z0dAzQ8DfPgnDLuqw/jgjL031iqAe287mle5Irvrn5j3BOCbLBPESNYcF6s2O3BNpcu9Z4Q4CPXOWaHrN5s4rhEI60waXPpvcjEbXULs6a69V3mbPzhPedAf3Ddo1u72HyWySzvfsKd8eorBemtAj4HVlGuw8HJB87HFGD7fnsN2jzZ/MVYLiDW+LFrdH0IvxJ4oiMuiMFW6PUqNYhx3CqEgJEGyPFnSOUhwseQ928jFZlDRh3lcpXSAKr3noZSYpuYbPs/pA0prSruQC3we9Jct+r0f/vfJ12PU6eYi5jcf3lGj9L/4CVTBgX/6Vn0gAAAAASUVORK5CYII=", tvLogoWhite: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAAmCAMAAACmhKjHAAAAbFBMVEUAAAD////////q9/1ux+tCteRSvOfZ8fr////I6vid2fL///+45Pb///////9jw+mQ1fCq3vT///////9KuOZbv+j///////////////9bwOl4y+2D0O7///+F0O////////////////87s+QdrEm+AAAAI3RSTlMAmU2f2fnspiWswAmzfBPfxrmPhvLmX1ZDOeXSzELMcmkcMMeAWbEAAAGzSURBVEjHxZXpkoMgEITXCIpK1jueuZb3f8dlmCSDYBJTtVXbvxD5ip6h0a8/17cQ4nPoeA5Q5yH7ADsEtrqt6FAFS9XbLHcBKE7ZOOanFNFhA3cwGFM3cRbCxPH7HbeDZSdliZfo9zUrYA1TS6UBavcC3BO3Qu6zpw3Vb0vli49FbMhnduHYG7WuCbpUZet50a9S9UxNol8ffEzsA1AOa3Ip8xXD2m7tYtk9ZoaDgU8y8No7HG4XStMaCWPpc0bL/hgujcgSgcShLm4+Q40hB03AZ0tRaLKozdrt9Lg41jPc5cJxgi3pSC76cbRWhMw4S7jDRapdBqiCimhFHN1yJukgkIOzBNJKNiNO3jYqIQ3kPmmoRwMlFGdbzRXKKnYijlvdrekO4hyW9yBDdFLaHJ7y9QFy5OLIPQI2p3DExJkGiUeNLXBYHgmz5ya/0RMYvKsezaqg8kh60r+iCIJq+KpBeZ5mA87LDKFVUK+HVJ4f0NZzcb8bVYCd95WQU+oY3UpBhlyxE1tyCTh1PsNTq94oKiC0HSBEbpL/leyrbZT/98q6etN2VB/p+rN7rR6of9MvcWpi5zjSB0oAAAAASUVORK5CYII=" }, e.logoSizes = { benzingapro: { width: 135, height: 25 }, bovespa: { width: 135, height: 50 }, cme: { width: 175, height: 26 }, currencywiki: { width: 135, height: 25 }, dailyfx: { width: 135, height: 25 }, fxstreet: { width: 137, height: 33 }, investopedia: { width: 135, height: 23 }, smartlab: { width: 135, height: 37 }, lse: { width: 135, height: 31 }, arabictrader: { width: 135, height: 33 } }
    }
});
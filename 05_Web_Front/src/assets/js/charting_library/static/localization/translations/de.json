{"ticks_slippage ... ticks": "ticks", "Months_interval": "<PERSON><PERSON>", "Percent_input": "Percent", "RSI Length_input": "RSI Länge", "month": "<PERSON><PERSON>", "roclen1_input": "roclen1", "Unmerge Down": "Nach unten auseinanderführen", "Percents": "Prozente", "Currency": "Währung", "Minor": "<PERSON>", "Do you really want to delete Chart Layout '{0}' ?": "Möchten Sie wirklich diese Chartskizze '{0}' löschen?", "June": "<PERSON><PERSON>", "Magnet Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Grand Supercycle": "Großer Superzyklus", "OSC_input": "OSC", "Realtime": "Exhtzeit", "Volume_study": "Volumen", "Lips_input": "Lips", "Histogram": "Histogramm", "Base Line_input": "Base Line", "Step": "Stuf<PERSON>", "Elliott Wave Circle": "<PERSON>", "Fib Time Zone": "Fib Zeitzonen", "SMALen2_input": "SMALen2", "Bollinger Bands_study": "Bollinger Bands", "Upper_input": "Ober", "Sig_input": "Sig", "Move Up": "<PERSON><PERSON>", "Callout": "Beschreibung", "Gann Square": "Gann Square (Quadrat)", "Count_input": "Count", "Full Circles": "Volle Kreise", "Industry": "Branche", "SMALen1_input": "SMALen1", "Cross_chart_type": "Kreuz Chart", "Target Color:": "<PERSON><PERSON><PERSON>l Farbe:", "Accumulation/Distribution_study": "Accumulation/Distribution", "Rate Of Change_study": "Rate Of Change", "Risk/Reward short": "Risiko/<PERSON>ewinn Short", "in_dates": "in", "Color 7_input": "Farbe 7", "Change Average HL value": "Durchschnittlicher HL Veränderungswert", "Trend-Based Fib Time": "Trend basierte Fi<PERSON>eit", "Remove All Indicators": "Alle Indikatoren entfernen", "Oscillator_input": "<PERSON><PERSON><PERSON><PERSON>", "Last Modified": "Zuletzt Verändert", "yay Color 0_input": "yay Color 0", "Labels": "Markierungen", "Chande Kroll Stop_study": "<PERSON><PERSON>", "Hours_interval": "Stunden", "Scale Right": "Skalierung rechts", "Money Flow_study": "Money Flow", "siglen_input": "siglen", "Indicator Labels": "<PERSON><PERSON><PERSON><PERSON>", "__specialSymbolOpen__Tomorrow at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__<PERSON><PERSON> bei__specialSymbolClose____dayTime__", "Remove All Drawing Tools": "Alle Zeichenwerkzeuge entfernen", "Linear Regression Curve_study": "Linear Regression Curve", "Symbol_input": "Symbol", "Upper Deviation_input": "Obere Abweichung", "Use Lower Deviation_input": "<PERSON>utze untere Abweichung", "__specialSymbolOpen__Last__specialSymbolClose__ __dayName__ __specialSymbolOpen__at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__letzter__specialSymbolClose____dayName____specialSymbolOpen__bei__specialSymbolClose____dayTime__", "Allow up to": "Ermöglicht bis zu", "Label": "<PERSON><PERSON><PERSON>", "Post Market": "<PERSON><PERSON>", "second": "seconds", "Any Number": "Jegliche <PERSON>ummer", "smoothD_input": "smoothD", "Falling_input": "Fallend", "Percentage": "Prozent", "Entry price:": "Einstiegspreis:", "RSI Source_input": "RSI Quelle", "Open Manage Drawings": "Zeichnungen verwalten öffnen", "Ichimoku Cloud_study": "Ichimoku Cloud", "jawLength_input": "<PERSON><PERSON><PERSON><PERSON>", "Apply Elliot Wave Major": "Haupt- <PERSON>", "Grid": "Gitter", "Mass Index_study": "Mass Index", "Smoothing_input": "Glättung", "Color 3_input": "Farbe 3", "Jaw Length_input": "Jaw Length", "Delete all drawing for this symbol": "Alle Zeichnungselemente löschen für dieses Symbol", "Keltner Channels_study": "Keltner Channels", "Long Position": "Long-Position", "Bands style_input": "Bänder-Stil", "Undo {0}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "With Markers": "<PERSON><PERSON>", "Momentum_study": "Momentum", "MF_input": "MF", "On Balance Volume_study": "On Balance Volume", "Hide Events on Chart": "Ereignisse im Chart ausblenden", "Change Hours To": "Wechsle Stunden zu", "Long length_input": "<PERSON>", "Apply Elliot Wave": "<PERSON> an<PERSON>", "Disjoint Angle": "<PERSON><PERSON><PERSON><PERSON>", "W_interval_short": "W", "Log Scale": "Logarithmische Skalierung", "Line - High": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "Zurich": "Zürich", "Equality Line_input": "Equality Line", "Open": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Fib Wedge": "<PERSON><PERSON>", "Line": "<PERSON><PERSON>", "Down fractals_input": "Down fractals", "smalen2_input": "smalen2", "isCentered_input": "isCentered", "Border": "<PERSON><PERSON><PERSON>", "Klinger Oscillator_study": "<PERSON>linger Oscillator", "Absolute": "Gesamt", "Style": "Stil", "SMI Ergodic Indicator/Oscillator_study": "SMI Ergodic Indicator/Oscillator", "Last available bar": "Letzter vorhandener Balken", "Manage Drawings": "Zeichnungen bearbeiten", "Analyze Trade Setup": "Trade Setup analysieren", "No drawings yet": "<PERSON>ch keine Zei<PERSON>nungen", "Chande MO_input": "<PERSON><PERSON>", "Copy link": "<PERSON>", "TRIX_study": "TRIX", "MACD_study": "MACD", "RVGI_input": "RVGI", "Last edited ": "Zuletzt verändert ", "signalLength_input": "signalLength", "Middle_input": "<PERSON><PERSON>", "d_dates": "t", "in %s_time_range": "in %s", "Recalculate After Order filled": "Neu Berechnen nach Ausführen der Order", "Source_compare": "Source", "Correlation Coefficient_study": "Correlation Coefficient", "Delayed": "Verzögert", "Bottom Labels": "Beschriftungen", "Text color": "Textfarbe", "Levels": "Level", "Short Length_input": "Short Length", "teethLength_input": "<PERSON><PERSON><PERSON><PERSON>", "Failure text color": "Textfarbe Misserfolg", "Hong Kong": "Hongkong", "FAILURE": "FEHLER", "Open {{symbol}} Text Note": "{{symbol}} <PERSON><PERSON><PERSON><PERSON>", "Lock All Drawing Tools": "Fixiere alle Zeichenwerkzeuge", "Target border color": "Randfarbe Kursziel", "Right End": "<PERSON><PERSON><PERSON> Ende", "Chaikin Oscillator_input": "Chaikin-Oszillator", "Head & Shoulders": "Kopf- & Schulterformation", "Do you really want to delete Study Template '{0}' ?": "Möchten Sie wirklich diese Studienvorlage '{0}' löschen?", "Favorite Drawings Toolbar": "Favoriten Leiste für Zeichnungen", "Properties...": "Eigenschaften...", "MA Cross_study": "MA Cross", "Trend Angle": "<PERSON><PERSON><PERSON>", "Crosshair": "Fadenkreuz", "Signal line period_input": "Singnallinienperiode", "Timezone/Sessions Properties...": "Zeitzone/Sitzungseinstellungen...", "Line Break": "Linienunterbrechung", "Show/Hide": "Anzeigen/Verbergen", "Price Volume Trend_study": "Price Volume Trend", "Auto Scale": "Auto Skalierung", "hour": "Stunde", "Scales": "<PERSON><PERSON><PERSON>", "Delete chart layout": "Chart Layout löschen", "F_data_mode_forbidden_letter": "F", "Risk/Reward long": "<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>", "Long RoC Length_input": "Lange Ro<PERSON>", "Length3_input": "Länge 3", "Cancel Order": "Auftrag abbrechen", "Chart Properties": "Chart Einstellungen", "Exit Full Screen (ESC)": "<PERSON><PERSON><PERSON><PERSON> (ESC)", "Show Bars Range": "<PERSON><PERSON><PERSON><PERSON> anzeigen", "Down Wave 2 or B": "Abwärtsbewegung 2 oder B", "%s ago_time_range": "%s vorher", "Zoom In": "Vergrößern", "Failure back color": "Hintergrundfarbe Misserfolg", "Below Bar": "Unterhalb der Bars", "Time Scale": "Zeitskala", "<p>Only <b>D, W, M</b> intervals are supported for this symbol/exchange. You will be automatically switched to a D interval. Intraday intervals are not available because of exchange policies.</p>": "<p>Nur<b>D,W,M</b>Intervalle sind für dieses Symbol oder Börse unterstützt. Das Chart wechselt automatisch zu einem D Intervall. Intraday Intervalle sind wegen Regulierungen der Börse nicht verfügbar.</p>", "Extend Left": "Nach links verlängern", "Date Range": "Datumsbereich", "Min Move": "<PERSON><PERSON>", "Price format is invalid.": "Preisformat ist ungültig.", "Show Price": "Preise anzeigen", "Level_input": "Level", "Commodity Channel Index_study": "Commodity Channel Index", "Elder's Force Index_input": "Elder's Force Index", "Scales Properties...": "Anzeige Einstellungen...", "Format": "Formatieren", "Color bars based on previous close": "Balken gemäß vorherigem Schlußkurs färben.", "Change band background": "Wechsle Band Hintergrund", "Marketplace Add-ons": "Handelsplatz Add-Ons", "Adjust Scale": "<PERSON><PERSON><PERSON> just<PERSON>en", "Anchored Text": "Verankerter Text", "Edit {0} Alert...": "{0} <PERSON><PERSON><PERSON> bearbeiten ...", "Aroon_study": "Aroon", "show MA_input": "show MA", "h_dates": "h", "Short Position": "Verkaufsposition", "Change Interval...": "Intervall ändern", "Apply Default": "Standard anwenden", "SMALen3_input": "SMALen3", "Average Directional Index_study": "Average Directional Index", "Fr_day_of_week": "Fr", "Invite-only script. Contact the author for more information.": "Auf-Einladung- Skript. Kontaktieren sie den Autor für nähere Informationen.", "Curve": "<PERSON><PERSON>", "H_in_legend": "Höchstkurs", "Bars Pattern": "<PERSON>", "D_input": "D", "Font Size": "Schriftgröße", "Change Interval": "Intervall ändern", "p_input": "p", "Chart layout name": "Chart Layout Name", "Fib Circles": "Fib Kreise", "Apply Manual Decision Point": "Manuellen Entscheidungspunkt verwenden", "Dot": "<PERSON><PERSON>", "Target back color": "Hintergrundfarbe Kursziel", "All": "Alle", "orders_up to ... orders": "orders", "Lead 2_input": "Lead 2", "Save image": "Bild speichern", "Fundamentals": "Fundamentaldaten", "Navigation Buttons": "Navigationstasten", "Miniscule": "Winzig", "Apply": "<PERSON><PERSON><PERSON>", "Precise Labels": "Genaue Beschriftungen", "%d day": "%d Tag", "Hide": "Verbergen", "Bottom": "Boden", "Target text color": "Textfarbe Kursziel", "Scale Left": "Skalierung links", "Elliott Wave Subminuette": "<PERSON><PERSON><PERSON><PERSON>", "Down Wave C": "Abwärtsbewegung C", "Text Alignment:": "Textausrichtung:", "Oct": "Okt", "Apply Elliot Wave Minor": "Minimale Elliot <PERSON> an<PERSON>", "Inputs": "Eingaben", "Conversion Line_input": "Conversion Line", "March": "<PERSON><PERSON><PERSON>", "Su_day_of_week": "Su", "Up fractals_input": "Up fractals", "Regression Trend": "Regressions Trendl<PERSON>e", "Fib Spiral": "<PERSON>b <PERSON>", "Double EMA_study": "Double EMA", "minute": "Minute", "Price Oscillator_study": "Price Oscillator", "Chop Zone_study": "Chop Zone", "Stop Color:": "Stop Farbe:", "Stay in Drawing Mode": "<PERSON><PERSON> b<PERSON>n", "Bottom Margin": "unterer Abstand", "Average True Range_study": "Average True Range", "Max value_input": "Maximalwert", "MA Length_input": "MA Length", "Invite-Only Scripts": "Auf-Einladung-Skripte", "Time Interval": "Zeitinterval", "UpperLimit_input": "OberesLimit", "sym_input": "sym", "DI Length_input": "DI Length", "Script Editor...": "Skript Editor", "Extend Lines": "<PERSON><PERSON>", "SMI_input": "SMI", "Change Days To": "Tage ändern zu", "Basis_input": "<PERSON><PERSON>", "Moving Average_study": "Moving Average", "lengthStoch_input": "lengthStoch", "Objects Tree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remove from favorites": "Aus Favoriten entfernen", "Copy": "<PERSON><PERSON><PERSON>", "Scale Series Only": "Nur Datenreihe skalieren", "Simple": "<PERSON><PERSON><PERSON>", "Report a data issue": "Ein Problem mit den Daten melden", "Arnaud Legoux Moving Average_study": "<PERSON><PERSON><PERSON> Moving Average", "Technical Analysis": "Technische Analyse", "Lower Band_input": "Unteres Band", "Verify Price for Limit Orders": "Preis für Limit-Orders überprüfen", "VI +_input": "VI +", "Line Width": "Lininenbreite", "Lead 1_input": "Lead 1", "Always Show Stats": "Zeige Statistiken immer", "Down Wave 4": "Abwärtsbewegung 4", "Down Wave 5": "Abwärtsbewegung 5", "Simple ma(signal line)_input": "Simple ma(signal line)", "Color 6_input": "Farbe 6", "Public Library": "Öffentliche Bibliothek", " Do you really want to delete Drawing Template '{0}' ?": " Möchten Sie die Zeichenvorlage '{0}' wirklich löschen?", "Down Wave 3": "Abwärtsbewegung 3", "Close message": "Nachricht schließen", "long_input": "long", "Chaikin Oscillator_study": "Chaikin Oscillator", "Balloon": "Sprechblase", "Market Open": "Börse g<PERSON>öffnet", "Color Theme": "Farbschema", "Centered_input": "<PERSON><PERSON><PERSON>", "Bollinger Bands Width_input": "Bollinger-Bänder-Breite", "Fib Speed Resistance Arcs": "Fib Speed Resistance Arcs (Bögen)", "Error occured while publishing": "Während des Publizierens ist ein Fehler aufgetreten", "Lock/Unlock": "Verriegeln/Entriegeln", "Color 1_input": "Farbe 1", "Moving Average Weighted_study": "Moving Average Weighted", "Type": "<PERSON><PERSON>", "Short period_input": "Kurze Periode", "Load Chart Layout": "Lade Chart Layout", "Fib Speed Resistance Fan": "Fib Speed Resistance Fan (Fächer)", "Left End": "link<PERSON>", "Volume Oscillator_study": "Volume Oscillator", "Always Visible": "<PERSON>mmer sichtbar", "S_data_mode_snapshot_letter": "S", "post-market": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Change Minutes To": "Wechsle Minuten zu", "Earnings breaks": "Earning<PERSON> Unterbrechsung", "Do not ask again": "Nicht erneut fragen", "Drawing Tools": "<PERSON><PERSON><PERSON>", "smalen4_input": "smalen4", "CCI_input": "CCI", "Unmerge Up": "Nach oben auseinanderführen", "increment_input": "Schrittweite", "XABCD Pattern": "XABCD Muster", "Schiff Pitchfork": "Schiff-Pitchfork", "Flipped": "Umgedreht", "DEMA_input": "DEMA", "NV_input": "NV", "Choppiness Index_study": "Choppiness Index", "Study Template '{0}' already exists. Do you really want to replace it?": "Studienvorlage '{0}' gibt es bereits. Möchten Sie es wirklich ersetzen?", "Merge Down": "Nach unten zusammenführen", " per contract": " pro Kontrakt", "eod delayed": "EOD verzögert", "Delete": "Löschen", "percent_input": "percent", "Length_input": "<PERSON><PERSON><PERSON>", "Avg HL in minticks": "Durchschnittliche HL in minticks", "Accumulation/Distribution_input": "Akkumulation/Distribution", "C_in_legend": "Schlusskurs", "Weeks_interval": "<PERSON><PERSON><PERSON>", "smoothK_input": "smoothK", "Percentage_scale_menu": "Prozentuale Skalierung", "Change Extended Hours": "Wechsle erweiterte Stunden", "MOM_input": "MOM", "h_interval_short": "{\"one\"=>\"h\"}", "Rotated Rectangle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Modified Schiff": "Modifizierte Schiff", "top": "Oberseite", "Send Backward": "<PERSON>ter nach hinten", "Mexico City": "Mexiko-Stadt", "TRIX_input": "TRIX", "Elliott Major Retracement": "Major <PERSON> Retracement", "Notification": "Benachrichtigung", "Fri": "Fr", "Periods_input": "<PERSON>en", "Forecast": "Prognose", "hour_plural": "Stunden", "Fraction part is invalid.": "Dieser Teil ist ungültig", "Connecting": "Wird verbunden", "Histogram_input": "Histogram", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StdDev_input": "StdAbw", "Change Minutes From": "<PERSON><PERSON><PERSON> Minuten von", "Relative Strength Index_study": "Relative Strength Index", "Interval is not applicable": "Intervall ist nicht anwendbar", "My Scripts": "<PERSON><PERSON>", "Monday": "Montag", "-DI_input": "-DI", "short_input": "short", "Symbol": "Symbol / Währungspaar", "Precision": "Präzision", "Please enter chart layout name": "<PERSON>te geben Si<PERSON> einen Namen für das Chart-Layout ein.", "Mar": "Mrz", "Offset": "Abstand", "Date": "Datum", "Format...": "Formatierung...", "__dayName__ __specialSymbolOpen__at__specialSymbolClose__ __dayTime__": "__dayName____specialSymbolOpen__bei__specialSymbolClose____dayTime__", "Search": "<PERSON><PERSON>", "Zig Zag_study": "Zig Zag", "Actual": "Ist", "SUCCESS": "ERFOLG", "Detrended Price Oscillator_input": "Trendbereinig<PERSON>-Oszillator", "Drawings Toolbar": "<PERSON><PERSON><PERSON>", "length_input": "length", "Close Position": "Posi<PERSON> s<PERSON><PERSON><PERSON>", "Price Line": "Preislinie", "Area With Breaks": "Fläche mit Lücken", "Zoom Out": "Verkleinern", "Stop Level. Ticks:": "Stop Wert. Ticks:", "Above Bar": "Über der Bar", "Visual Order": "Sichtbare Ordnung", "__specialSymbolOpen__Yesterday at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__Gestern bei__specialSymbolClose____dayTime__", "Stop Background Color": "Hintergrundfarbe beenden", "Slow length_input": "Slow length", "Conversion Line Periods_input": "Conversion Line Periods", "Sector": "Se<PERSON><PERSON>", "Stochastic_study": "Stochastic", "Apply WPT Down Wave": "WPT Down Wave anwenden", "Marker Color": "Stiftfarbe", "TEMA_input": "TEMA", "Apply WPT Up Wave": "WPT Up Wave anwenden", "Directional Movement_study": "Directional Movement", "Extend Left End": "Linkes Ende verlängern", "Advance/Decline_study": "Advance/Decline", "Flag Mark": "Flagge", "Drawings": "Z<PERSON><PERSON>nungen", "Fast length_input": "Fast length", "Cancel": "Abbrechen", "Bar #": "Balken Nr.", "Median_input": "Median", "Redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hide Drawings Toolbar": "Zeichnen Werkzeugleiste ausblenden", "Ultimate Oscillator_study": "Ultimate Oscillator", "Growing_input": "Wachsend", "Angle": "<PERSON><PERSON>", "%d year_plural": "%d <PERSON><PERSON>re", "Plot_input": "<PERSON><PERSON><PERSON><PERSON>", "Color 8_input": "Farbe 8", "Indicators, Fundamentals, Economy and Add-ons": "Indikatoren, Fundamental- und Wirtschaftsdaten und Add-ons", "Bollinger Bands Width_study": "Bollinger Bands Width", "roclen3_input": "roclen3", "Overbought_input": "Überkauft", "DPO_input": "DPO", "No study templates saved": "<PERSON><PERSON> Indikatorvorlagen gespeichert", "Trend Line": "<PERSON><PERSON><PERSON>", "Relative Vigor Index_study": "Relative Vigor Index", "Circle": "<PERSON><PERSON><PERSON>", "Price Range": "Preisspanne", "Extended Hours": "Verlängerte Handelszeit", "Triangle": "<PERSON><PERSON><PERSON>", "Line With Breaks": "<PERSON><PERSON> mit Lücken", "Period_input": "Periode", "Watermark": "Wasserzeichen", "Trigger_input": "Auslöser", "SigLen_input": "SigLen", "Clone": "Klonen", "Color 2_input": "Farbe 2", "Show Prices": "Preise anzeigen", "Timezone/Sessions": "Zeitzone/Handelszeit", "Graphics": "Grafiken", "Arrow Mark Right": "<PERSON><PERSON><PERSON> nach rechts", "Background color 2": "Hintergrundfarbe 2", "Background color 1": "Hintergrundfarbe 1", "Circles": "Kreise", "Vortex Indicator_study": "Vortex Indicator", "Williams Alligator_study": "Williams Alligator", "ROCLen1_input": "ROCLen1", "M_interval_short": "M", "Change Symbol...": "Symbol ändern...", "Price Levels": "Preisniveaus", "Source text color": "Textfarbe Ursprung", "Zero Line_input": "Null<PERSON>e", "__specialSymbolOpen__Today at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__Heute bei__specialSymbolClose____dayTime__", "Increment_input": "Schrittweite", "Days_interval": "Tages", "Net Volume_study": "Net Volume", "Show Alert Labels": "Alarmbezeichnungen anzeigen", "m_dates": "m", "Lock": "Fixieren", "length14_input": "length14", "retrying": "wied<PERSON><PERSON><PERSON>", "High": "Hoch", "Date and Price Range": "Daten und Preis Range", "Polyline": "Linienzug", "Reconnect": "Neu verbinden", "Add to favorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "Color 0_input": "Farbe 0", "maximum_input": "maximum", "D_data_mode_delayed_letter": "D", "Sigma_input": "Sigma", "Coordinates": "Koordinaten", "fastLength_input": "<PERSON><PERSON><PERSON><PERSON>", "Width": "Breite", "Historical Volatility_study": "Historical Volatility", "Compare or Add Symbol...": "Symbol hinzufügen oder vergleichen...", "Parallel Channel": "<PERSON><PERSON><PERSON>", "Time Cycles": "Zeitzyklen", "Divisor_input": "Divisor", "Down Wave 1 or A": "Abwärtsbewegung 1 oder A", "ROC_input": "ROC", "Dec": "<PERSON>z", "Extend": "Verlängern", "length7_input": "length7", "Toggle Maximize Chart": "Schalte den maximalen Chart ein", "Undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Window Size_input": "Fenstergröße", "Reset Scale": "Skalierung zurücksetzen", "Long Length_input": "<PERSON>", "True Strength Indicator_study": "True Strength Indicator", "%R_input": "%R", "Instrument is not allowed": "Dieses Instrument ist nicht erlaubt.", "bars_margin": "<PERSON><PERSON><PERSON>", "Initial capital": "Anfangskapital", "Indicator Last Value": "Letzter Wert des Indikators", "More features on tradingview.com": "Mehr Funktionen auf tradingview.com", "smalen3_input": "smalen3", "Length1_input": "Länge 1", "Always Invisible": "<PERSON>mmer verborgen", "Days": "Tage", "x_input": "x", "Save As...": "Speichern unter...", "Elliott Double Combo Wave (WXY)": "<PERSON> (WXY)", "Parabolic SAR_study": "Parabolic SAR", "Fisher Transform_study": "Fisher Transform", "Hollow Candles": "<PERSON><PERSON>e Candlesticks", "Any Symbol": "Jegliches Symbol", "UO_input": "UO", "Contracts": "Gratulation", "Minutes": "Minuten", "Short RoC Length_input": "Short RoC Length", "Show Orders": "Orders anzeigen", "Jaw_input": "<PERSON><PERSON>", "Help": "<PERSON><PERSON><PERSON>", "Coppock Curve_study": "Coppock Curve", "Reset Chart": "Chart zurücksetzen", "Themes": "Designs", "Left Axis": "Linke Achse", "Overlay the main chart": "<PERSON><PERSON><PERSON><PERSON>", "longlen_input": "longlen", "Moving Average Exponential_study": "Moving Average Exponential", "Source border color": "Randfarbe Ursprung", "Redo {0}": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cypher Pattern": "<PERSON><PERSON>", "s_dates": "s", "Move Down": "<PERSON><PERSON>", "Area": "Fläche", "invalid symbol": "ungültiges symbol", "Triangle Pattern": "<PERSON><PERSON><PERSON>", "Gann Fan": "<PERSON><PERSON>", "Balance of Power_study": "Balance of Power", "EOM_input": "EOM", "Apply Manual Risk/Reward": "<PERSON><PERSON>/Re<PERSON> verwenden", "Market Closed": "<PERSON><PERSON> geschlossen", "Indicators": "Indikatoren", "q_input": "q", "%D_input": "%D", "Border Color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Offset_input": "Offset", "Price Scale": "Preisskala", "HV_input": "HV", "Start_input": "<PERSON><PERSON><PERSON>", "R_data_mode_realtime_letter": "R", "Hours": "Stunden", "Send to Back": "Ganz nach hinten", "Color 4_input": "Farbe 4", "Angles": "<PERSON><PERSON><PERSON>", "Prices": "<PERSON><PERSON>", "Extended Hours (Intraday Only)": "Stunden erweitern (nur Intraday)", "July": "<PERSON><PERSON>", "Create Horizontal Line": "Horizontale <PERSON>", "Cycle": "Z<PERSON><PERSON>", "ADX Smoothing_input": "ADX Glättung", "One color for all lines": "Eine Farbe für alle Linien", "Settings": "Einstellungen", "Candles": "<PERSON><PERSON><PERSON>", "We_day_of_week": "We", "Pre Market": "<PERSON><PERSON>", "Width (% of the Box)": "Breite (% der Box)", "%d minute": "%d Minute", "week_plural": "<PERSON><PERSON><PERSON>", "Pip Size": "Pip-Größe", "This drawing is used in alert. If you remove the drawing, the alert will be also removed. Do you want to remove the drawing anyway?": "<PERSON><PERSON>nung wird in einem Alarm benutzt. Wenn <PERSON> diese Zeichnung entfernen, wird der Alarm auch gelöscht. Möchten Sie diese Zeichnung trotzdem löschen?", "Hide All Drawing Tools": "Alle Zeichenwerkzeuge verbergen", "MA_input": "MA", "Detrended Price Oscillator_study": "Detrended Price Oscillator", "not authorized": "Nicht autorisiert", "Image URL": "Bild URL", "SMI Ergodic Oscillator_input": "SMI Ergodic Oscillator", "Show Objects Tree": "Objektbaum anzeigen", "Primary": "<PERSON><PERSON><PERSON><PERSON>", "Price:": "Preis:", "Gann Box": "Gann-Box", "Bring to Front": "Ganz nach vorne", "Brush": "<PERSON><PERSON>l", "Not Now": "<PERSON><PERSON>t nicht", "lengthRSI_input": "lengthRSI", "Events & Alerts": "Events & Alarme", "+DI_input": "+DI", "Apply Default Drawing Template": "Voreingestellte Vorlage anwenden", "Invalid Symbol": "Ungültiges Symbol", "Inside Pitchfork": "Innenliegende Pitchfork", "yay Color 1_input": "yay Color 1", "CHOP_input": "CHOP", "Note": "Anmerkung", "Show Countdown": "Countdown anzeigen", "WMA Length_input": "WMA Length", "Show Dividends on Chart": "Zeige Dividende auf den Charts", "Borders": "<PERSON><PERSON><PERSON>", "month_plural": "Monate", "loading...": "lade...", "Closed_line_tool_position": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Columns": "Spalten", "Change Resolution": "Auflösung ändern", "Indicator Arguments": "Funktionsargument des Indikators", "Create Vertical Line": "<PERSON><PERSON><PERSON><PERSON>", "%d minute_plural": "%d Minuten", "Degree": "Grad", " per order": " pro Order", "Line - HL/2": "Linie - HT/2", "Least Squares Moving Average_study": "Least Squares Moving Average", "Change Variance value": "Wechsle Varianz wert", "Source_input": "<PERSON><PERSON>", "Change Seconds To": "Wechsle Sekunden zu", "%K_input": "%K", "Success back color": "Hintergrundfarbe Erfolg", "Please enter template name": "<PERSON>te geben Si<PERSON> einen Namen für die Vorlage ein.", "Events Breaks": "Unterbruch", "Months": "Monate", "Elliott Wave Minor": "<PERSON> <PERSON>", "Measure (Shift + Click on the chart)": "<PERSON><PERSON><PERSON><PERSON> (Shift + Klick auf den Chart)", "Override Min Tick": "<PERSON> ü<PERSON>chreiben", "Add To Text Notes": "<PERSON><PERSON><PERSON> zu Textnotizen hinzu", "Elliott Triple Combo Wave (WXYXZ)": "<PERSON>mbo Welle (WXYXZ)", "Multiplier_input": "Multiplikator", "Base Line Periods_input": "Base Line Periods", "Show Dividends": "Zeige die Dividenden an", "pre-market": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Top Labels": "Markierungen an Oberseite", "Line - Open": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "%d day_plural": "%d <PERSON><PERSON>", "Elliott Triangle Wave (ABCDE)": "<PERSON> (ABCDE)", "Text Wrap": "Zeilenumbruch", "Elliott Minor Retracement": "Minor <PERSON> Retracement", "Th_day_of_week": "Th", "No symbols matched your criteria": "Kein passendes Symbol / Währungspaar gefunden", "Icon": "Symbol", "Short_input": "<PERSON><PERSON>", "Heikin Ashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Indicator_input": "<PERSON><PERSON><PERSON><PERSON>", "Open Interval Dialog": "Intervall-<PERSON><PERSON>", "Athens": "<PERSON><PERSON>", "Q_input": "Q", "Content": "Inhalt", "middle": "{\"one\"=>\"Mi<PERSON>\"}", "Lock Cursor In Time": "Sperre den Zeiger in Zeit", "Eraser": "<PERSON><PERSON><PERSON>", "TimeZone": "Zeitzone", "Envelope_study": "Envelope", "Active Symbol": "Aktives Symbol", "Horizontal Line": "Horizontale <PERSON>", "O_in_legend": "Eröffnungskurs", "Confirmation": "Bestätigung", "Add Alert": "Mel<PERSON>ng hinzufügen", "Lines:": "<PERSON><PERSON>", "Hide Favorite Drawings Toolbar": "Favorisierte Zeichentoolbar verstecken", "useTrueRange_input": "useTrueRange", "Profit Level. Ticks:": "Gewinnspanne. Ticks:", "Show Date/Time Range": "Datums-/Zeitspanne anzeigen", "%d year": "%d <PERSON><PERSON>r", "Horz Grid Lines": "<PERSON><PERSON>", "Text Notes are available only on chart page. Please <a href=\"/chart/\">open a chart</a> and then try again.": "Textnotizen sind verfügbar nur auf der Chartseite. Bitte <a href=\"/chart/\">öffne einen Chart</a> und versuche Sie es erneut.", "Tu_day_of_week": "Tu", "day": "Tag", "deviation_input": "<PERSON>bwei<PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "Base currency": "Basis Währung", "VWMA_study": "VWMA", "Success text color": "Textfarbe Erfolg", "ADX smoothing_input": "ADX Glättung", "%d hour": "%d Stunde", "Order size": "Umfang der Order", "Displacement_input": "Displacement", "ADR_B_input": "ADR_B", "Chaikin Money Flow_study": "Chaikin Money Flow", "Ease Of Movement_study": "Ease Of Movement", "Defaults": "Standardeinstellungen", "Oversold_input": "Überverkauft", "Williams %R_study": "Williams %R", "depth_input": "depth", "RSI_input": "RSI", "Long period_input": "<PERSON>", "Mo_day_of_week": "Mo", "center": "zentrieren", "Vertical Line": "<PERSON><PERSON><PERSON><PERSON>", "Sorry, the Copy Link button doesn't work in your browser. Please select the link and copy it manually.": "Entschuldigung, <PERSON> K<PERSON><PERSON> \"Link-Adresse kopieren\" funktioniert nicht in Ihrem Browser. Bitte markieren Sie das Link und kopieren Sie es manuell.", "X_input": "X", "C_data_mode_connecting_letter": "C", "Simple ma(oscillator)_input": "Simple ma(oscillator)", "SMALen4_input": "SMALen4", "ROCLen4_input": "ROCLen4", "Aroon Down_input": "<PERSON><PERSON>", "Add To Watchlist": "Zur Beobachtungsliste hinzufügen", "Extend Right": "Nach rechts verlängern", "left": "links", "Lock scale": "Skalierung feststellen", "Time Levels": "Zeitebenen", "Arrow": "Pfeil", "smalen1_input": "smalen1", "Drawing Template '{0}' already exists. Do you really want to replace it?": "Zeichenvorlage '{0}' gibt es bereits. Möchten Sie es wirklich wirklich ersetzen?", "Extend Right End": "Rechtes Ende verlängern", "Fans": "<PERSON><PERSON><PERSON>", "Price_input": "Pre<PERSON>", "Close_input": "Close", "Arrow Mark Down": "<PERSON><PERSON><PERSON> nach unten", "Modified Schiff Pitchfork": "Modifizierte Schiff-Pitchfork", "Relative Volatility Index_study": "Relative Volatility Index", "Elliott Impulse Wave (12345)": "<PERSON> (12345)", "PVT_input": "PVT", "Circle Lines": "Kreislinien", "Hull Moving Average_study": "Hull Moving Average", "Bring Forward": "<PERSON><PERSON> nach vorne", "Friday": "Freitag", "Zero_input": "<PERSON><PERSON>", "Company Comparison": "Unternehmensvergleich", "Stochastic Length_input": "Stochastische Länge", "mult_input": "mult", "Signal smoothing_input": "Signalglättung", "E_data_mode_end_of_day_letter": "E", "Trend-Based Fib Extension": "Trend basierte Fib Extension", "Double Curve": "Doppel-<PERSON><PERSON>", "Stochastic RSI_study": "Stochastic RSI", "Horizontal Ray": "Horizontaler <PERSON>", "Ok": "In Ordnung", "Edit Order": "Auftrag bearbeiten", "Error:": "<PERSON><PERSON>:", "Fullscreen mode": "Vollbildmodus", "Add Text Note For {0}": "Textnotiz für {0} hinzufügen", "K_input": "K", "In Session": "In der Sitzung", "ROCLen3_input": "ROCLen3", "Micro": "<PERSON><PERSON><PERSON>", "Text Color": "Textfarbe", "Extend Alert Line": "Alarm Lin<PERSON>", "Oops!": "Huch!", "New Zealand": "Neuseeland", "Apply Defaults": "Voreinstellungen anwenden", "Screen (No Scale)": "Bildschirm (ohne Skalierung)", "Extended Alert Line": "Alarm Lin<PERSON>", "Signal_input": "Signal", "OK": "In Ordnung", "like": "likes", "Show": "Anzeigen", "Exchange": "Börse", "{0} bars": "{0} <PERSON><PERSON><PERSON>", "Lower_input": "Unter", "Created ": "<PERSON><PERSON><PERSON><PERSON> ", "Arc": "Bogen", "Elder's Force Index_study": "Elder's Force Index", "Do you really want to delete Color Theme '{0}' ?": "Möchten Sie wirklich die Farbenmotiv '{0}' löschen?", "%d month_plural": "%d <PERSON><PERSON>", "Low": "Tief", "Bollinger Bands %B_study": "Bollinger Bands %B", "Time Zone": "Zeitzone", "right": "rechts", "%d month": "%d <PERSON><PERSON>", "Donchian Channels_study": "Donchian Channels", "Upper Band_input": "Oberes Band", "February": "<PERSON><PERSON><PERSON>", "start_input": "start", "No indicators matched your criteria.": "<PERSON><PERSON> passenden Indikatoren gefunden", "Commission": "Kommission", "Short length_input": "<PERSON><PERSON><PERSON>", "Kolkata": "<PERSON><PERSON><PERSON><PERSON>", "Triple EMA_study": "Triple EMA", "Precise Labels_scale_menu": "Precise Labels", "Smoothed Moving Average_study": "Smoothed Moving Average", "Chatham Islands": "Chatham Inseln", "Channel": "<PERSON><PERSON>", "FXCM CFD data is available only to FXCM account holders": "FXCM CFD Informationen sind nur für Inhaber eines FXCM Kontos verfügbar.", "Lagging Span 2 Periods_input": "Lagging Span 2 Periods", "Connecting Line": "<PERSON><PERSON>", "day_plural": "Tage", "bottom": "Unterseite", "Teeth_input": "<PERSON><PERSON>", "Moscow": "<PERSON><PERSON><PERSON>", "Fib Channel": "<PERSON><PERSON>", "Visibility": "Sichtbarkeit", "Events": "<PERSON><PERSON><PERSON><PERSON>", "Minutes_interval": "Minuten", "Insert Study Template": "Indikatorvorlage einfügen", "exponential_input": "exponentiell", "%d hour_plural": "%d St<PERSON>en", "OnBalanceVolume_input": "OnBalanceVolume", "roclen2_input": "roclen2", "Chande Momentum Oscillator_study": "Chande Momentum Oscillator", "Not applicable": "<PERSON><PERSON> anwen<PERSON>bar", "or copy url:": "oder URL kopieren:", "Bollinger Bands %B_input": "Bollinger-Bänder %B", "Indicator Values": "Wert des Indikators", "Lips Length_input": "Lips Length", "Use Upper Deviation_input": "Benutze obere Abweichung", "L_in_legend": "<PERSON><PERSON><PERSON><PERSON>", "Inside": "Innerhalb", "minute_plural": "Minuten", "shortlen_input": "shortlen", "Quotes are delayed by {0} min": "Preise sind um {0} Minuten verzögert", "Copied to clipboard": "In die Zwischenablage kopiert", "ADX_input": "ADX", "Profit Background Color": "Gewinn Hintergrund Farbe", "Bar's Style": "Balken Darstellung", "Exponential_input": "Exponentiell", "ROCLen2_input": "ROCLen2", "Previous": "<PERSON><PERSON><PERSON><PERSON>", "Stay In Drawing Mode": "<PERSON><PERSON> b<PERSON>n", "Comment": "Kommentar", "Long_input": "<PERSON>", "Bars": "<PERSON><PERSON><PERSON>", "Show Labels": "Markierungen anzeigen", "Flat Top/Bottom": "Flache Ober-/Unterseite", "loading data": "laden von daten", "December": "Dezember", "Lock drawings": "Zeichnungen sperren", "Border color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Change Seconds From": "Wechsle Sekunden von", "Left Labels": "linke Markierungen", "Insert Indicator...": "Indikator einfügen...", "P_input": "P", "Paste %s": "Einfügen %s", "Invite-only script. You have been granted access.": "Auf-Einladung-Skript. Sie haben Zugang erhalten.", "Ray": "<PERSON><PERSON><PERSON>", "ATR Length": "ATR- Länge", "Rectangle": "<PERSON><PERSON><PERSON>", "Source back color": "Hintergrundfarbe Ursprung", "Transparency": "Transparenz", "No": "<PERSON><PERSON>", "All Indicators And Drawing Tools": "Alle Indikatoren und Zeichenwerkzeuge.", "Cyclic Lines": "Zyklische Linien", "length28_input": "length28", "ABCD Pattern": "<PERSON><PERSON>", "When selecting this checkbox the study template will set \"__interval__\" interval on a chart": "<PERSON>n diese Checkbox ausgewählt wird, stellt die Indikatorvorlage das __interval__-Intervall auf dem Chart ein.", "Add": "Hinzufügen", "Line - Low": "<PERSON><PERSON> <PERSON><PERSON>", "Millennium": "Jahrtausend", "Price Label": "<PERSON><PERSON><PERSON>", "Apply Indicator on {0} ...": "<PERSON><PERSON><PERSON><PERSON> anwen<PERSON> auf {0}", "NEW": "NEU", "Wick": "<PERSON><PERSON>", "Hull MA_input": "Hull MA", "Lock Scale": "Skalierung fixieren", "distance: {0}": "Abstand: {0}", "Extended": "verl<PERSON><PERSON><PERSON>", "Arcs": "Bögen", "Top Margin": "<PERSON>berer A<PERSON>d", "Length2_input": "Länge 2", "Insert Drawing Tool": "Zeichenwerkzeug einfügen", "Show Price Range": "Preisspanne anzeigen", "Correlation_input": "Korrelation", "Scales Text": "Achsenbeschriftung", "Session Breaks": "Ende der Handelszeit", "Add {0} To Watchlist": "{0} zur Beobachtungsliste hinzufügen", "Anchored Note": "Dauerhafte Notiz", "lipsLength_input": "<PERSON><PERSON><PERSON><PERSON>", "Apply Indicator on {0}": "<PERSON><PERSON><PERSON><PERSON> anwen<PERSON> auf {0}", "roclen4_input": "roclen4", "closed": "geschlossen", "Background Color": "Hintergrundfarbe", "D_data_mode_delayed_streaming_letter": "D", "VI -_input": "VI -", "slowLength_input": "<PERSON><PERSON><PERSON><PERSON>", "Click to set a point": "<PERSON><PERSON><PERSON>, um einen Punkt zu setzen", "January": "<PERSON><PERSON><PERSON>", "n/a": "k.A.", "Indicator Titles": "Titel des Indikators", "Sa_day_of_week": "Sa", "Change area background": "Wechsle Hintergrund", "Error": "<PERSON><PERSON>", "Edit Position": "Position bearbeiten", "RVI_input": "RVI", "Awesome Oscillator_study": "Awesome Oscillator", "Do you really want to delete Drawing Template '{0}' ?": "Möchten Sie wirklich die Zeichenvorlage '{0}' löschen?", "Left": "Links", "Show Text": "Text anzeigen", "Objects Tree...": "Objekt-<PERSON><PERSON>...", "Compare": "Vergleichen", "Add Symbol": "Symbol hinzufügen", "Projection": "Projektion", "Track time": "Verfolge <PERSON>", "Enter a new chart layout name": "Chart Layout neu benennen", "Signal Length_input": "Signallänge", "Properties": "Eigenschaften", "Teeth Length_input": "Teeth Length", "Point Value": "Punktwert", "D_interval_short": "D", "Close": "<PERSON><PERSON><PERSON><PERSON>", "ParabolicSAR_input": "ParabolicSAR", "Log Scale_scale_menu": "Logarithmische Skalierung", "MACD_input": "MACD", "Do not show this message again": "<PERSON><PERSON><PERSON>t nicht erneut anzeigen", "Arrow Mark Left": "Pfeil nach links", "Source Code...": "Quellcode", "Line - Close": "Linie - Schlusspreis", "Confirm Inputs": "Eingabe bestätigen", "Open_line_tool_position": "Eröffnungskurs", "Lagging Span_input": "Lagging Span", "Cross": "Kreuz", "Mirrored": "Gespiegelt", "Price": "Pre<PERSON>", "Elliott Correction Wave (ABC)": "<PERSON> (ABC)", "Error while trying to create snapshot.": "Fehler während der Erstellung eines Schnappschuss", "Label Background": "Markierungshintergrund", "Please report the issue or click Reconnect.": "Bitte melden Sie das Problem und klicken Neu verbinden", "1. Slide your finger to select location for first anchor<br>2. Tap anywhere to place the first anchor": "1. Bewegen Sie Ihren Finger zur Position des ersten Ankerpunkts.<br/>2. Durch Antippen einer beliebigen Stelle wird der erste Ankerpunkt gesetzt.", "%": "Prozent", "May": "<PERSON>", "Are you sure?": "Sind Sie sicher?", "Color 5_input": "Farbe 5", "McGinley Dynamic_study": "McGinley Dynamic", "Default": "Standard", "auto_scale": "auto", "Background": "Hi<PERSON>grund", "% of equity": "% Aktien", "Apply Elliot Wave Intermediate": "Fortgeschrittene Elliot Wellen anwenden", "VWMA_input": "VWMA", "Lower Deviation_input": "Untere Abweichung", "ATR_input": "ATR", "Extend Lines Left": "<PERSON><PERSON> nach <PERSON>", "Reverse": "Invertieren", "Oops, something went wrong": "<PERSON><PERSON>, etwas ging schief", "Shapes_input": "Formen", "Median": "Median Wert", "Fisher_input": "<PERSON>", "Remove": "Entfernen", "len_input": "len", "Arrow Mark Up": "<PERSON><PERSON><PERSON> nach oben", "Crosses_input": "Crosses", "KST_input": "KST", "LowerLimit_input": "UntereBegrenzung", "Know Sure Thing_study": "Know Sure Thing", "Copy Chart Layout": "Chart Layout kopieren", "Compare...": "Vergleichen...", "1. Slide your finger to select location for next anchor<br>2. Tap anywhere to place the next anchor": "1. Bewegen Sie Ihren Finger zur Position des nächsten Ankerpunkts.<br/>2. Durch Antippen einer beliebigen Stelle wird der nächste Ankerpunkt gesetzt.", "Compare or Add Symbol": "Symbol hinzufügen oder vergleichen", "Color": "Farbe", "Aroon Up_input": "Aroon Tief", "Singapore": "Singapur", "Scales Lines": "Achsenlinien", "Type the interval number for munute charts (i.e. 5 if it is going to be a five minute chart). Or number plus letter for H (Hourly), D (Daily), W (Weekly), M (Monthly) intervals (i.e. D or 2H)": "Geben Sie das Interval für die Minuten-Charts ein (z.B. 5, wenn es ein 5-Minuten-Chart sein soll), oder eine Zahl plus einen Buchstaben für H (stündliche), D (tägliche), W (wöchentliche), M (monatliche) Intervalle (z.B. D oder 2H).", "Show Distance": "Abstand anzeigen", "Risk/Reward Ratio: {0}": "Rendite/Risi<PERSON>: {0}", "Williams Fractal_study": "<PERSON>", "Merge Up": "Nach oben zusammenführen", "Right Margin": "<PERSON><PERSON><PERSON>", "Ellipse": "Elypse", "Warsaw": "Warschau"}
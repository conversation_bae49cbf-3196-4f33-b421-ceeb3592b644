<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>币严 | 币严官网 - 全球比特币交易平台 | 全球数字货币交易平台</title>
    <meta name="keyword" content="区块链资产交易平台,币币交易,数字货币交易所,虚拟货币账户,比特币交易系统,以太坊行情,莱特币走势,BZB,币严,币严官网,bizzan,BIZZAN,BTC价格,ETH钱包注册,LTC市价,杠杆期货,Bizzan,数字货币" />
    <meta name="description" content="币严是全球领先的数字货币交易平台，提供比特币、以太坊、BZB、USDT等多种数字资产交易" />
    <link href="https://www.bizzan.top" rel="canonical"/>
    <meta property="og:title" content="币严 | 比特币交易平台 | 数字货币交易平台" class="next-head" class="next-head"/>
    <meta property="og:site_name" content="Bizzan" class="next-head" class="next-head"/>
    <meta property="og:image" content="https://bizzanex.oss-cn-hangzhou.aliyuncs.com/oglogo.png" class="next-head"/>
    <meta property="twitter:title" content="币严 | 比特币交易平台 | 数字货币交易平台" class="next-head"/>
    <meta property="twitter:site" content="Bizzan" class="next-head"/>
    <meta property="twitter:image" content="https://bizzanex.oss-cn-hangzhou.aliyuncs.com/oglogo.png" class="next-head"/>
    <meta property="twitter:image:src" content="https://bizzanex.oss-cn-hangzhou.aliyuncs.com/oglogo.png" class="next-head"/>
    <meta property="twitter:card" content="summary_large_image" class="next-head"/>
    <meta name="robots" content="index" class="next-head"/>
    <meta property="og:description" content="币严是全球领先的数字货币交易平台，提供比特币、以太坊、BZB、USDT等多种数字资产交易" class="next-head"/>
    <meta property="twitter:description" content="币严是全球领先的数字货币交易平台，提供比特币、以太坊、BZB、USDT等多种数字资产交易" class="next-head"/>
    <link rel="icon" href="/favicon.ico">

    <script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>

    <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=09c7e4c3-04d2-4ec6-bbf0-484bd271a172"> </script>
    <style>
        html, body{
            background: #141e2c;
        }
        .init-loading{
            width: 98%;
            height: 20px;
            position:absolute;
            top: 40%;
            text-align: center;
            z-index: 9999;
        }
        .loading span {
            display: inline-block;
            vertical-align: middle;
            width: .6em;
            height: .6em;
            margin: .19em;
            background: #007DB6;
            border-radius: .6em;
            -webkit-animation: loading 1s infinite alternate;
            animation: loading 1s infinite alternate;
        }

        /*
        * Dots Colors
        * Smarter targeting vs nth-of-type?
        */
        .loading span:nth-of-type(2) {
            background: #008FB2;
            -webkit-animation-delay: 0.2s;
            animation-delay: 0.2s;
        }
        .loading span:nth-of-type(3) {
            background: #009B9E;
            -webkit-animation-delay: 0.4s;
            animation-delay: 0.4s;
        }
        .loading span:nth-of-type(4) {
            background: #00A77D;
            -webkit-animation-delay: 0.6s;
            animation-delay: 0.6s;
        }
        .loading span:nth-of-type(5) {
            background: #00B247;
            -webkit-animation-delay: 0.8s;
            animation-delay: 0.8s;
        }
        .loading span:nth-of-type(6) {
            background: #5AB027;
            -webkit-animation-delay: 1.0s;
            animation-delay: 1.0s;
        }
        .loading span:nth-of-type(7) {
            background: #A0B61E;
            -webkit-animation-delay: 1.2s;
            animation-delay: 1.2s;
        }

        /*
        * Animation keyframes
        * Use transition opacity instead of keyframes?
        */
        @-webkit-keyframes loading {
            0% {
                opacity: 0;
            }
            100% {
                opacity: 1;
            }
        }
        @keyframes loading {
            0% {
                opacity: 0;
            }
            100% {
                opacity: 1;
            }
        }
        .loading-text {
            color: #f0a70a;
            margin: 0;
            font: .8em verdana;
            text-transform: uppercase;
            letter-spacing: .1em;
        }
        .loading-text2{
            color: #828ea1;
            margin-top: 130px;
            font: .7em verdana;
            letter-spacing: .1em;
            display: none;
        }
        .loading-text2 a{
            color: #828ea1;
        }
    </style>
</head>

<body>
    <!-- TradingView Widget BEGIN -->
    <!-- <img style="display: none;" src="https://bizzanex.oss-cn-hangzhou.aliyuncs.com/oglogo.png"></img> -->
<!-- TradingView Widget END -->
    <div class="init-loading" id="initLoading">
        <div class="loading">
            <h2 class="loading-text">www.bizzan.top</h2>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <h2 class="loading-text2" id="browseTips">注意：IE浏览器如长时间无法加载完成，请使用 <a href="https://www.google.cn/chrome/" target="_blank">Chrome浏览器</a> 或 <a href="https://www.firefox.com.cn/" target="_blank">Firefox浏览器</a> 打开本站</h2>
        </div>
    </div>
    <div id="app"></div>
</body>
</html>
